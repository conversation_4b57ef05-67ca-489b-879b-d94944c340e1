<?php

declare(strict_types=1);

/**
 * Comave SellerCategoryRestriction Plugin to Block Category Management Actions
 *
 * @category  Comave
 * @package   Comave_SellerCategoryRestriction
 * <AUTHOR>
 * @copyright Copyright (c) Comave
 */

namespace Comave\SellerCategoryRestriction\Plugin;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\UrlInterface;

class BlockSellerCategoryActions
{
    private const ERROR_MESSAGE = 'Category management is restricted to administrators only.';
    private const SELLER_DASHBOARD_PATH = 'marketplace/account/dashboard';

    public function __construct(
        private readonly ResultFactory $resultFactory,
        private readonly ManagerInterface $messageManager,
        private readonly UrlInterface $url
    ) {}

    /**
     * Block access to all seller category management actions
     *
     * @param ActionInterface $subject
     * @param callable $proceed
     * @return Redirect
     */
    public function aroundExecute(ActionInterface $subject, callable $proceed): Redirect
    {
        $this->messageManager->addErrorMessage(__(self::ERROR_MESSAGE));

        $result = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $result->setUrl($this->url->getUrl(self::SELLER_DASHBOARD_PATH));

        return $result;
    }
}
