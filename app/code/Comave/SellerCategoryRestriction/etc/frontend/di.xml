<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Webkul\MpSellerCategory\Controller\Category\Manage">
        <plugin name="comave_block_seller_category_manage"
                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
                sortOrder="1"/>
    </type>

    <type name="Webkul\MpSellerCategory\Controller\Category\Save">
        <plugin name="comave_block_seller_category_save"
                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
                sortOrder="1"/>
    </type>

    <type name="Webkul\MpSellerCategory\Controller\Category\Edit">
        <plugin name="comave_block_seller_category_edit"
                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
                sortOrder="1"/>
    </type>

    <type name="Webkul\MpSellerCategory\Controller\Category\NewAction">
        <plugin name="comave_block_seller_category_new"
                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
                sortOrder="1"/>
    </type>

    <type name="Webkul\MpSellerCategory\Controller\Category\Delete">
        <plugin name="comave_block_seller_category_delete"
                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
                sortOrder="1"/>
    </type>
</config>
