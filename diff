diff --git a/app/code/Comave/SellerCategoryRestriction/Plugin/BlockSellerCategoryActions.php b/app/code/Comave/SellerCategoryRestriction/Plugin/BlockSellerCategoryActions.php
new file mode 100644
index *********..a9f179d08
--- /dev/null
+++ b/app/code/Comave/SellerCategoryRestriction/Plugin/BlockSellerCategoryActions.php
@@ -0,0 +1,49 @@
+<?php
+
+declare(strict_types=1);
+
+/**
+ * Comave SellerCategoryRestriction Plugin to Block Category Management Actions
+ *
+ * @category  Comave
+ * @package   Comave_SellerCategoryRestriction
+ * <AUTHOR>
+ * @copyright Copyright (c) Comave
+ */
+
+namespace Comave\SellerCategoryRestriction\Plugin;
+
+use Magento\Framework\App\ActionInterface;
+use Magento\Framework\Controller\ResultFactory;
+use Magento\Framework\Controller\Result\Redirect;
+use Magento\Framework\Message\ManagerInterface;
+use Magento\Framework\UrlInterface;
+
+class BlockSellerCategoryActions
+{
+    private const ERROR_MESSAGE = 'Category management is restricted to administrators only.';
+    private const SELLER_DASHBOARD_PATH = 'marketplace/account/dashboard';
+
+    public function __construct(
+        private readonly ResultFactory $resultFactory,
+        private readonly ManagerInterface $messageManager,
+        private readonly UrlInterface $url
+    ) {}
+
+    /**
+     * Block access to all seller category management actions
+     *
+     * @param ActionInterface $subject
+     * @param callable $proceed
+     * @return Redirect
+     */
+    public function aroundExecute(ActionInterface $subject, callable $proceed): Redirect
+    {
+        $this->messageManager->addErrorMessage(__(self::ERROR_MESSAGE));
+
+        $result = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
+        $result->setUrl($this->url->getUrl(self::SELLER_DASHBOARD_PATH));
+
+        return $result;
+    }
+}
diff --git a/app/code/Comave/SellerCategoryRestriction/etc/frontend/di.xml b/app/code/Comave/SellerCategoryRestriction/etc/frontend/di.xml
new file mode 100644
index *********..b13c6304d
--- /dev/null
+++ b/app/code/Comave/SellerCategoryRestriction/etc/frontend/di.xml
@@ -0,0 +1,34 @@
+<?xml version="1.0"?>
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
+
+    <type name="Webkul\MpSellerCategory\Controller\Category\Manage">
+        <plugin name="comave_block_seller_category_manage"
+                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
+                sortOrder="1"/>
+    </type>
+
+    <type name="Webkul\MpSellerCategory\Controller\Category\Save">
+        <plugin name="comave_block_seller_category_save"
+                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
+                sortOrder="1"/>
+    </type>
+
+    <type name="Webkul\MpSellerCategory\Controller\Category\Edit">
+        <plugin name="comave_block_seller_category_edit"
+                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
+                sortOrder="1"/>
+    </type>
+
+    <type name="Webkul\MpSellerCategory\Controller\Category\NewAction">
+        <plugin name="comave_block_seller_category_new"
+                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
+                sortOrder="1"/>
+    </type>
+
+    <type name="Webkul\MpSellerCategory\Controller\Category\Delete">
+        <plugin name="comave_block_seller_category_delete"
+                type="Comave\SellerCategoryRestriction\Plugin\BlockSellerCategoryActions"
+                sortOrder="1"/>
+    </type>
+</config>
diff --git a/app/code/Comave/SellerCategoryRestriction/etc/module.xml b/app/code/Comave/SellerCategoryRestriction/etc/module.xml
new file mode 100644
index *********..fe1dd68f9
--- /dev/null
+++ b/app/code/Comave/SellerCategoryRestriction/etc/module.xml
@@ -0,0 +1,10 @@
+<?xml version="1.0"?>
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
+    <module name="Comave_SellerCategoryRestriction">
+        <sequence>
+            <module name="Webkul_Marketplace"/>
+            <module name="Webkul_MpSellerCategory"/>
+        </sequence>
+    </module>
+</config>
diff --git a/app/code/Comave/SellerCategoryRestriction/registration.php b/app/code/Comave/SellerCategoryRestriction/registration.php
new file mode 100644
index *********..5a9a516a3
--- /dev/null
+++ b/app/code/Comave/SellerCategoryRestriction/registration.php
@@ -0,0 +1,11 @@
+<?php
+
+declare(strict_types=1);
+
+use Magento\Framework\Component\ComponentRegistrar;
+
+ComponentRegistrar::register(
+    ComponentRegistrar::MODULE,
+    'Comave_SellerCategoryRestriction',
+    __DIR__
+);
diff --git a/app/code/Comave/SellerCategoryRestriction/view/frontend/layout/customer_account.xml b/app/code/Comave/SellerCategoryRestriction/view/frontend/layout/customer_account.xml
new file mode 100644
index *********..d29e67c52
--- /dev/null
+++ b/app/code/Comave/SellerCategoryRestriction/view/frontend/layout/customer_account.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0"?>
+<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
+    <body>
+        <referenceBlock name="seller_manage_category_link" remove="true"/>
+    </body>
+</page>
diff --git a/app/code/Comave/SellerCategoryRestriction/view/frontend/layout/default.xml b/app/code/Comave/SellerCategoryRestriction/view/frontend/layout/default.xml
new file mode 100644
index *********..*********
--- /dev/null
+++ b/app/code/Comave/SellerCategoryRestriction/view/frontend/layout/default.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0"?>
+<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
+    <body>
+        <referenceBlock name="seller_manage_category_link_layout2" remove="true"/>
+    </body>
+</page>
diff --git a/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/layout2/account/navigation.phtml b/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/layout2/account/navigation.phtml
index 1b00ed79f..559e8557e 100644
--- a/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/layout2/account/navigation.phtml
+++ b/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/layout2/account/navigation.phtml
@@ -153,20 +153,6 @@
                                                 </a>
                                             </li>
                                         <?php endif; ?>
-                                        <?php if ($isPartner): ?>
-                                            <?php if (($isSellerGroup &&
-                                                    $_helper->isAllowedAction('mpsellercategory/category/')) || !$isSellerGroup): ?>
-                                                <li class="level-2">
-                                                    <a
-                                                        href=
-                                                        "<?=  $escaper->escapeUrl($block->getUrl('mpsellercategory/category/manage', ['_secure' => 1])); ?>"
-                                                        class=
-                                                        "<?=  $escaper->escapeUrl(strpos($magentoCurrentUrl, 'mpsellercategory/category/')? "active":"");?>">
-                                                        <?=  $escaper->escapeHtml(__('Manage Categories')) ?>
-                                                    </a>
-                                                </li>
-                                            <?php endif; ?>
-                                        <?php endif; ?>
                                         <strong class="wk-mp-submenu-group-title">
                                             <span><?= /* @noEscape */ __('Connect Shopify')?></span>
                                         </strong>
diff --git a/app/etc/config.php b/app/etc/config.php
index 36f73177e..b440ec5ed 100644
--- a/app/etc/config.php
+++ b/app/etc/config.php
@@ -879,6 +879,7 @@ return [
         'Webkul_MpVendorAttributeManager' => 1,
         'Webkul_Mppercountryperproductshipping' => 1,
         'Comave_OutOfStockNotification' => 1,
+        'Comave_SellerCategoryRestriction' => 1,
         'Webkul_PriceDropAlert' => 1,
         'WeltPixel_AdvanceCategorySorting' => 1,
         'WeltPixel_AdvancedWishlist' => 1,
